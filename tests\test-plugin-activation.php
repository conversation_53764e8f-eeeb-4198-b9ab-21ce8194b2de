<?php

/**
 * Test plugin activation and basic functionality
 */
class Test_Plugin_Activation extends WP_UnitTestCase {
	
	/**
	 * Test plugin activation
	 */
	public function test_plugin_activation() {
		// Test that plugin constants are defined
		$this->assertTrue( defined( 'CAJ_ETPU_VERSION' ) );
		$this->assertTrue( defined( 'CAJ_ETPU_PLUGIN_DIR' ) );
		$this->assertTrue( defined( 'CAJ_ETPU_PLUGIN_URL' ) );
		
		// Test version constant
		$this->assertEquals( '3.0.0', CAJ_ETPU_VERSION );
	}
	
	/**
	 * Test WordPress version requirement
	 */
	public function test_wordpress_version_requirement() {
		global $wp_version;
		
		// Plugin requires WordPress 6.0+
		$this->assertTrue( version_compare( $wp_version, '6.0', '>=' ) );
	}
	
	/**
	 * Test PHP version requirement
	 */
	public function test_php_version_requirement() {
		// Plugin requires PHP 7.4+
		$this->assertTrue( version_compare( PHP_VERSION, '7.4', '>=' ) );
	}
	
	/**
	 * Test that admin class exists and initializes
	 */
	public function test_admin_class_exists() {
		$this->assertTrue( class_exists( 'CAJ_ETPU_Admin' ) );
	}
	
	/**
	 * Test that upgrader classes exist
	 */
	public function test_upgrader_classes_exist() {
		// Load the upgrader classes
		require_once( CAJ_ETPU_PLUGIN_DIR . 'custom-theme-upgrader.php' );
		require_once( CAJ_ETPU_PLUGIN_DIR . 'custom-plugin-upgrader.php' );
		
		$this->assertTrue( class_exists( 'CAJ_ETPU_Theme_Upgrader' ) );
		$this->assertTrue( class_exists( 'CAJ_ETPU_Plugin_Upgrader' ) );
	}
	
	/**
	 * Test that utility classes can be loaded
	 */
	public function test_utility_classes_load() {
		// Test logger class
		if ( file_exists( CAJ_ETPU_PLUGIN_DIR . 'includes/class-logger.php' ) ) {
			require_once( CAJ_ETPU_PLUGIN_DIR . 'includes/class-logger.php' );
			$this->assertTrue( class_exists( 'CAJ_ETPU_Logger' ) );
		}
		
		// Test settings class
		if ( file_exists( CAJ_ETPU_PLUGIN_DIR . 'includes/class-settings.php' ) ) {
			require_once( CAJ_ETPU_PLUGIN_DIR . 'includes/class-settings.php' );
			$this->assertTrue( class_exists( 'CAJ_ETPU_Settings' ) );
		}
		
		// Test user feedback class
		if ( file_exists( CAJ_ETPU_PLUGIN_DIR . 'includes/class-user-feedback.php' ) ) {
			require_once( CAJ_ETPU_PLUGIN_DIR . 'includes/class-user-feedback.php' );
			$this->assertTrue( class_exists( 'CAJ_ETPU_User_Feedback' ) );
		}
	}
	
	/**
	 * Test plugin options are set correctly on activation
	 */
	public function test_activation_options() {
		// Simulate plugin activation
		caj_etpu_activate();
		
		// Check that version option is set
		$this->assertEquals( CAJ_ETPU_VERSION, get_option( 'caj_etpu_version' ) );
		
		// Check that activation timestamp is set
		$this->assertNotFalse( get_option( 'caj_etpu_activated' ) );
		$this->assertTrue( is_numeric( get_option( 'caj_etpu_activated' ) ) );
	}
	
	/**
	 * Test plugin deactivation
	 */
	public function test_plugin_deactivation() {
		// Set up activation first
		caj_etpu_activate();
		$this->assertNotFalse( get_option( 'caj_etpu_activated' ) );
		
		// Test deactivation
		caj_etpu_deactivate();
		$this->assertFalse( get_option( 'caj_etpu_activated' ) );
		
		// Version should still be there (not removed on deactivation)
		$this->assertEquals( CAJ_ETPU_VERSION, get_option( 'caj_etpu_version' ) );
	}
	
	/**
	 * Test that required WordPress functions exist
	 */
	public function test_required_wordpress_functions() {
		$required_functions = array(
			'wp_upload_dir',
			'wp_normalize_path',
			'wp_is_file_mod_allowed',
			'current_user_can',
			'check_admin_referer',
			'wp_rand',
			'plugin_dir_path',
			'plugin_dir_url',
		);
		
		foreach ( $required_functions as $function ) {
			$this->assertTrue( function_exists( $function ), "Required function {$function} does not exist" );
		}
	}
	
	/**
	 * Test that required WordPress classes exist
	 */
	public function test_required_wordpress_classes() {
		// Load WordPress upgrader classes
		require_once( ABSPATH . 'wp-admin/includes/class-wp-upgrader.php' );
		
		$required_classes = array(
			'WP_Upgrader',
			'Theme_Upgrader',
			'Plugin_Upgrader',
			'Theme_Installer_Skin',
			'Plugin_Installer_Skin',
			'File_Upload_Upgrader',
		);
		
		foreach ( $required_classes as $class ) {
			$this->assertTrue( class_exists( $class ), "Required class {$class} does not exist" );
		}
	}
	
	/**
	 * Test plugin file structure
	 */
	public function test_plugin_file_structure() {
		$required_files = array(
			'init.php',
			'admin.php',
			'custom-theme-upgrader.php',
			'custom-plugin-upgrader.php',
			'readme.txt',
			'uninstall.php',
		);

		foreach ( $required_files as $file ) {
			$file_path = CAJ_ETPU_PLUGIN_DIR . $file;
			$this->assertTrue( file_exists( $file_path ), "Required file {$file} does not exist" );
		}

		// Test optional include files
		$optional_files = array(
			'includes/class-logger.php',
			'includes/class-settings.php',
			'includes/class-user-feedback.php',
			'assets/admin.css',
		);

		foreach ( $optional_files as $file ) {
			$file_path = CAJ_ETPU_PLUGIN_DIR . $file;
			if ( file_exists( $file_path ) ) {
				$this->assertFileIsReadable( $file_path, "Optional file {$file} exists but is not readable" );
			}
		}
	}
}
