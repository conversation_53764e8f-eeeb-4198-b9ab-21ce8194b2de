<?php

/**
 * Test logger functionality
 */
class Test_Logger extends WP_UnitTestCase {
	
	/**
	 * Set up test environment
	 */
	public function setUp(): void {
		parent::setUp();
		
		// Load logger class
		if ( file_exists( CAJ_ETPU_PLUGIN_DIR . 'includes/class-logger.php' ) ) {
			require_once( CAJ_ETPU_PLUGIN_DIR . 'includes/class-logger.php' );
		}
	}
	
	/**
	 * Clean up after tests
	 */
	public function tearDown(): void {
		// Clean up log files
		if ( class_exists( 'CAJ_ETPU_Logger' ) ) {
			CAJ_ETPU_Logger::clear_log();
		}
		
		parent::tearDown();
	}
	
	/**
	 * Test logger class exists
	 */
	public function test_logger_class_exists() {
		$this->assertTrue( class_exists( 'CAJ_ETPU_Logger' ) );
	}
	
	/**
	 * Test logger initialization
	 */
	public function test_logger_initialization() {
		if ( ! class_exists( 'CAJ_ETPU_Logger' ) ) {
			$this->markTestSkipped( 'Logger class not available' );
		}
		
		// Test that logger can be initialized
		CAJ_ETPU_Logger::init();
		
		// Test log file path
		$log_file = CAJ_ETPU_Logger::get_log_file();
		$this->assertNotEmpty( $log_file );
		$this->assertStringContainsString( 'caj-etpu-debug.log', $log_file );
	}
	
	/**
	 * Test logging functionality
	 */
	public function test_logging_functionality() {
		if ( ! class_exists( 'CAJ_ETPU_Logger' ) ) {
			$this->markTestSkipped( 'Logger class not available' );
		}
		
		// Enable logging for testing
		CAJ_ETPU_Logger::set_logging_enabled( true );
		$this->assertTrue( CAJ_ETPU_Logger::is_logging_enabled() );
		
		// Test different log levels
		CAJ_ETPU_Logger::error( 'Test error message' );
		CAJ_ETPU_Logger::warning( 'Test warning message' );
		CAJ_ETPU_Logger::info( 'Test info message' );
		CAJ_ETPU_Logger::debug( 'Test debug message' );
		
		// Check that log file was created
		$log_file = CAJ_ETPU_Logger::get_log_file();
		$this->assertFileExists( $log_file );
		
		// Check log content
		$log_content = file_get_contents( $log_file );
		$this->assertStringContainsString( 'Test error message', $log_content );
		$this->assertStringContainsString( 'Test warning message', $log_content );
		$this->assertStringContainsString( 'Test info message', $log_content );
		$this->assertStringContainsString( 'Test debug message', $log_content );
		
		// Check log levels in content
		$this->assertStringContainsString( '[ERROR]', $log_content );
		$this->assertStringContainsString( '[WARNING]', $log_content );
		$this->assertStringContainsString( '[INFO]', $log_content );
		$this->assertStringContainsString( '[DEBUG]', $log_content );
	}
	
	/**
	 * Test logging with context
	 */
	public function test_logging_with_context() {
		if ( ! class_exists( 'CAJ_ETPU_Logger' ) ) {
			$this->markTestSkipped( 'Logger class not available' );
		}
		
		CAJ_ETPU_Logger::set_logging_enabled( true );
		
		$context = array(
			'user_id' => 123,
			'action' => 'theme_upgrade',
			'theme_name' => 'test-theme',
		);
		
		CAJ_ETPU_Logger::info( 'Theme upgrade started', $context );
		
		$log_file = CAJ_ETPU_Logger::get_log_file();
		$log_content = file_get_contents( $log_file );
		
		$this->assertStringContainsString( 'Theme upgrade started', $log_content );
		$this->assertStringContainsString( 'Context:', $log_content );
		$this->assertStringContainsString( 'user_id', $log_content );
		$this->assertStringContainsString( 'theme_upgrade', $log_content );
	}
	
	/**
	 * Test log clearing
	 */
	public function test_log_clearing() {
		if ( ! class_exists( 'CAJ_ETPU_Logger' ) ) {
			$this->markTestSkipped( 'Logger class not available' );
		}
		
		CAJ_ETPU_Logger::set_logging_enabled( true );
		CAJ_ETPU_Logger::info( 'Test message before clear' );
		
		$log_file = CAJ_ETPU_Logger::get_log_file();
		$this->assertFileExists( $log_file );
		
		// Clear log
		CAJ_ETPU_Logger::clear_log();
		$this->assertFileDoesNotExist( $log_file );
	}
	
	/**
	 * Test recent entries retrieval
	 */
	public function test_recent_entries() {
		if ( ! class_exists( 'CAJ_ETPU_Logger' ) ) {
			$this->markTestSkipped( 'Logger class not available' );
		}
		
		CAJ_ETPU_Logger::set_logging_enabled( true );
		
		// Log multiple messages
		for ( $i = 1; $i <= 5; $i++ ) {
			CAJ_ETPU_Logger::info( "Test message {$i}" );
		}
		
		// Get recent entries
		$recent_entries = CAJ_ETPU_Logger::get_recent_entries( 3 );
		
		$this->assertCount( 3, $recent_entries );
		$this->assertStringContainsString( 'Test message 5', $recent_entries[2] );
		$this->assertStringContainsString( 'Test message 4', $recent_entries[1] );
		$this->assertStringContainsString( 'Test message 3', $recent_entries[0] );
	}
	
	/**
	 * Test logging disabled state
	 */
	public function test_logging_disabled() {
		if ( ! class_exists( 'CAJ_ETPU_Logger' ) ) {
			$this->markTestSkipped( 'Logger class not available' );
		}
		
		// Disable logging
		CAJ_ETPU_Logger::set_logging_enabled( false );
		$this->assertFalse( CAJ_ETPU_Logger::is_logging_enabled() );
		
		// Try to log a message
		CAJ_ETPU_Logger::info( 'This should not be logged' );
		
		// Check that no log file was created
		$log_file = CAJ_ETPU_Logger::get_log_file();
		$this->assertFileDoesNotExist( $log_file );
	}
	
	/**
	 * Test log constants
	 */
	public function test_log_constants() {
		if ( ! class_exists( 'CAJ_ETPU_Logger' ) ) {
			$this->markTestSkipped( 'Logger class not available' );
		}
		
		$this->assertEquals( 'error', CAJ_ETPU_Logger::LEVEL_ERROR );
		$this->assertEquals( 'warning', CAJ_ETPU_Logger::LEVEL_WARNING );
		$this->assertEquals( 'info', CAJ_ETPU_Logger::LEVEL_INFO );
		$this->assertEquals( 'debug', CAJ_ETPU_Logger::LEVEL_DEBUG );
		$this->assertEquals( 1048576, CAJ_ETPU_Logger::MAX_LOG_SIZE );
	}
}
