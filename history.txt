1.0.0 - 2011-07-06 - <PERSON>
	Release-ready
1.0.1 - 2011-09-28 - <PERSON>
	Fixed an issue with running upgrades on multisite networks
1.0.2 - 2013-08-20 - <PERSON>
	Removed a stray <i> tag in the Install Plugins screen that caused problems with installing plugins on WPEngine sites.
1.0.3 - 2014-04-18 - <PERSON> Jean
	Fixed issue with theme upgrades working properly in WordPress 3.9.
1.0.4 - 2014-05-29 - <PERSON>
	Updated instructions on how to upgrade themes.
1.0.5 - 2016-07-13 - <PERSON>atibility Fix: Added support for PHP 7+.
1.0.6 - 2016-07-19 - <PERSON>x: Fixed incorrect handling of some zip file formats. Thanks to the team at https://kairaweb.com/ for helping solve this issue.
2.0.0 - 2016-08-15 - <PERSON>: Removed the requirement for the user to select "Yes" from the drop down in order to initiate an update. This new version does not change the appearance of the upload form. Instead, it automatically creates a backup and performs an upgrade if the supplied plugin or theme already exists.
	Enhancement: If a zip backup file cannot be created, the old directory is renamed to a new name in order to still keep a backup.
	Enhancement: Updated the code to use a better way of integrating the upgrade logic. This approach should greatly reduce the potential for conflicts with other code or site configurations.
	Enhancement: The backup details now are found in the same format as the rest of the upgrade messages.
2.0.1 - 2018-10-01 - Chris Jean
	Compatibility Update: Updated to indicate compatibility with WordPress 4.9.8.
3.0.0 - 2025-01-18 - Compatibility Update
	Compatibility Update: Updated for WordPress 6.8 and PHP 8.2+ compatibility.
	Security Enhancement: Removed debug code that was enabled in production.
	Security Enhancement: Added proper input validation and path sanitization.
	Security Enhancement: Added wp_is_file_mod_allowed() checks for upload permissions.
	Bug Fix: Fixed file path handling for better cross-platform compatibility.
	Bug Fix: Replaced rand() with wp_rand() for better security.
	Enhancement: Improved error handling and removed suppressed errors.
	Enhancement: Added proper security checks and modernized code structure.
	Enhancement: Updated to use modern WordPress APIs and best practices.
	Enhancement: Added comprehensive PHPDoc documentation.
	Enhancement: Improved memory limit and time limit handling.
2.0.2 - 2021-06-08 - Chris Jean
	Misc: Version bump to show that this project is still active. No code changes are needed for functionality to continue.
