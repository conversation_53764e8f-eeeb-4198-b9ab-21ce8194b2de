<?php

// Prevent direct access
if ( ! defined( 'ABSPATH' ) ) {
	exit;
}

// Load logger if available
if ( defined( 'CAJ_ETPU_PLUGIN_DIR' ) && file_exists( CAJ_ETPU_PLUGIN_DIR . 'includes/class-logger.php' ) ) {
	require_once( CAJ_ETPU_PLUGIN_DIR . 'includes/class-logger.php' );
}

// Load user feedback if available
if ( defined( 'CAJ_ETPU_PLUGIN_DIR' ) && file_exists( CAJ_ETPU_PLUGIN_DIR . 'includes/class-user-feedback.php' ) ) {
	require_once( CAJ_ETPU_PLUGIN_DIR . 'includes/class-user-feedback.php' );
}

class CAJ_ETPU_Plugin_Upgrader extends Plugin_Upgrader {

	/**
	 * Install a plugin package
	 *
	 * @param array $args Installation arguments
	 * @return bool|WP_Error Installation result
	 */
	public function install_package( $args = array() ) {
		global $wp_filesystem;

		// Log the start of installation
		if ( class_exists( 'CAJ_ETPU_Logger' ) ) {
			CAJ_ETPU_Logger::info( 'Starting plugin installation/upgrade', array(
				'source' => isset( $args['source'] ) ? $args['source'] : 'unknown',
				'destination' => isset( $args['destination'] ) ? $args['destination'] : 'unknown'
			) );
		}

		if ( empty( $args['source'] ) || empty( $args['destination'] ) ) {
			// Only run if the arguments we need are present.
			if ( class_exists( 'CAJ_ETPU_Logger' ) ) {
				CAJ_ETPU_Logger::warning( 'Missing source or destination arguments, falling back to parent method' );
			}
			return parent::install_package( $args );
		}

		$source_files = array_keys( $wp_filesystem->dirlist( $args['source'] ) );
		$remote_destination = $wp_filesystem->find_folder( $args['destination'] );

		// Locate which directory to copy to the new folder, This is based on the actual folder holding the files.
		if ( 1 === count( $source_files ) && $wp_filesystem->is_dir( trailingslashit( $args['source'] ) . $source_files[0] . '/' ) ) { // Only one folder? Then we want its contents.
			$destination = trailingslashit( $remote_destination ) . trailingslashit( $source_files[0] );
		} elseif ( 0 === count( $source_files ) ) {
			// Looks like an empty zip, we'll let the default code handle this.
			return parent::install_package( $args );
		} else { // It's only a single file, the upgrader will use the folder name of this file as the destination folder. Folder name is based on zip filename.
			$destination = trailingslashit( $remote_destination ) . trailingslashit( basename( $args['source'] ) );
		}

		if ( is_dir( $destination ) ) {
			// This is an upgrade, clear the destination.
			$args['clear_destination'] = true;

			// Switch template strings to use upgrade terminology rather than install terminology.
			$this->upgrade_strings();

			// Replace default remove_old string to make the messages more meaningful.
			$this->strings['installing_package'] = __( 'Upgrading the plugin&#8230;', 'easy-theme-and-plugin-upgrades' );
			$this->strings['remove_old'] = __( 'Backing up the old version of the plugin&#8230;', 'easy-theme-and-plugin-upgrades' );
		}

		return parent::install_package( $args );
	}

	/**
	 * Get plugin data from directory
	 *
	 * @param string $directory Plugin directory path
	 * @return array|false Plugin data or false
	 */
	private function caj_get_plugin_data( $directory ) {
		$files = glob( trailingslashit( $directory ) . '*.php' );

		if ( $files ) {
			foreach ( $files as $file ) {
				$info = get_plugin_data( $file, false, false );

				if ( ! empty( $info['Name'] ) ) {
					$data = array(
						'name'    => $info['Name'],
						'version' => $info['Version'],
					);

					return $data;
				}
			}
		}

		return false;
	}

	/**
	 * Clear the destination directory and create a backup
	 *
	 * @param string $destination Destination directory path
	 * @return bool|WP_Error Clear result
	 */
	public function clear_destination( $destination ) {
		global $wp_filesystem;

		// Validate destination path
		if ( empty( $destination ) || ! is_string( $destination ) ) {
			return new WP_Error( 'invalid_destination', __( 'Invalid destination path provided.', 'easy-theme-and-plugin-upgrades' ) );
		}

		// Sanitize the destination path
		$destination = wp_normalize_path( $destination );

		if ( ! is_dir( $destination ) ) {
			// This is an installation not an upgrade.
			return parent::clear_destination( $destination );
		}

		$data = $this->caj_get_plugin_data( $destination );

		if ( false === $data ) {
			// The existing directory is not a valid plugin, skip backup.
			return parent::clear_destination( $destination );
		}

		$backup_url = $this->caj_create_backup( $destination );

		if ( ! is_wp_error( $backup_url ) ) {
			/* translators: 1: plugin zip URL */
			$this->skin->feedback( sprintf( __( 'A backup zip file of the old plugin version can be downloaded <a href="%1$s">here</a>.', 'easy-theme-and-plugin-upgrades' ), $backup_url ) );

			// Restore default strings and display the original remove_old message.
			$this->upgrade_strings();
			$this->skin->feedback( 'remove_old' );

			return parent::clear_destination( $destination );
		}

		$this->skin->error( $backup_url );
		$this->skin->feedback( __( 'Moving the old version of the plugin to a new directory&#8230;', 'easy-theme-and-plugin-upgrades' ) );

		$new_name = basename( $destination ) . "-{$data['version']}";
		$directory = dirname( $destination );

		for ( $x = 0; $x < 20; $x++ ) {
			$test_name = $new_name . '-' . $this->get_random_characters( 10, 20 );

			if ( ! is_dir( $directory . '/' . $test_name ) ) {
				$new_name = $test_name;
				break;
			}
		}

		if ( is_dir( $directory . '/' . $new_name ) ) {
			// We gave it our best effort. Time to give up on the idea of having a backup.
			$this->skin->error( __( 'Unable to find a new directory name to move the old version of the plugin to. No backup will be created.', 'easy-theme-and-plugin-upgrades' ) );
		} else {
			$result = $wp_filesystem->move( $destination, $directory . '/' . $new_name );

			if ( $result ) {
				/* translators: 1: new plugin directory name */
				$this->skin->feedback( sprintf( __( 'Moved the old version of the plugin to a new plugin directory named %1$s. This directory should be backed up and removed from the site.', 'easy-theme-and-plugin-upgrades' ), "<code>$new_name</code>" ) );
			} else {
				$this->skin->error( __( 'Unable to move the old version of the plugin to a new directory. No backup will be created.', 'easy-theme-and-plugin-upgrades' ) );
			}
		}

		// Restore default strings and display the original remove_old message.
		$this->upgrade_strings();
		$this->skin->feedback( 'remove_old' );

		return parent::clear_destination( $destination );
	}

	/**
	 * Create a backup of the plugin directory
	 *
	 * @param string $directory Plugin directory path
	 * @return string|WP_Error Backup URL or error
	 */
	private function caj_create_backup( $directory ) {
		require_once( ABSPATH . 'wp-admin/includes/file.php' );

		// Validate input
		if ( empty( $directory ) || ! is_dir( $directory ) ) {
			return new WP_Error( 'invalid_directory', __( 'Invalid plugin directory provided for backup.', 'easy-theme-and-plugin-upgrades' ) );
		}

		$wp_upload_dir = wp_upload_dir();

		// Check for upload errors
		if ( ! empty( $wp_upload_dir['error'] ) ) {
			return new WP_Error( 'upload_dir_error', sprintf( __( 'Upload directory error: %s', 'easy-theme-and-plugin-upgrades' ), $wp_upload_dir['error'] ) );
		}

		$zip_path = $wp_upload_dir['path'];
		$zip_url  = $wp_upload_dir['url'];

		if ( ! is_dir( $zip_path ) ) {
			return new WP_Error( 'caj-etpu-cannot-backup-no-destination-path', __( 'A plugin backup can not be created since a destination path for the backup file could not be found.', 'easy-theme-and-plugin-upgrades' ) );
		}

		if ( ! is_writable( $zip_path ) ) {
			return new WP_Error( 'caj-etpu-backup-directory-not-writable', __( 'A plugin backup can not be created because the backup directory is not writable.', 'easy-theme-and-plugin-upgrades' ) );
		}

		$data = $this->caj_get_plugin_data( $directory );

		$rand_string = $this->get_random_characters( 10, 20 );

		// Sanitize filename components for security
		$safe_dirname = sanitize_file_name( basename( $directory ) );
		$safe_version = sanitize_file_name( $data['version'] );
		$zip_file = $safe_dirname . '-' . $safe_version . '-' . $rand_string . '.zip';

		// Performance optimizations for backup creation
		$this->optimize_for_backup_creation();

		// Reduce the chance that a timeout will occur while creating the zip file.
		$this->set_time_limit_safely( 600 );

		// Attempt to increase memory limits.
		$this->set_minimum_memory_limit( '256M' );

		$zip_path .= '/' . $zip_file;
		$zip_url  .= '/' . $zip_file;

		// Check if PclZip class is available
		if ( ! class_exists( 'PclZip' ) ) {
			if ( ! file_exists( ABSPATH . 'wp-admin/includes/class-pclzip.php' ) ) {
				return new WP_Error( 'pclzip_not_available', __( 'PclZip class is not available for creating backup files.', 'easy-theme-and-plugin-upgrades' ) );
			}
			require_once( ABSPATH . 'wp-admin/includes/class-pclzip.php' );
		}

		$archive = new PclZip( $zip_path );

		$zip_result = $archive->create( $directory, PCLZIP_OPT_REMOVE_PATH, dirname( $directory ) );

		if ( 0 === $zip_result ) {
			/* translators: 1: zip error details */
			return new WP_Error( 'caj-etpu-cannot-backup-zip-failed', sprintf( __( 'A plugin backup can not be created as creation of the zip file failed with the following error: %1$s', 'easy-theme-and-plugin-upgrades' ), $archive->errorInfo( true ) ) );
		}

		$attachment = array(
			'post_mime_type' => 'application/zip',
			'guid'           => $zip_url,
			/* translators: 1: plugin name, 2: plugin version */
			'post_title'     => sprintf( __( 'Plugin Backup - %1$s - %2$s', 'easy-theme-and-plugin-upgrades' ), $data['name'], $data['version'] ),
			'post_content'   => '',
		);

		// Ensure required functions are available
		if ( ! function_exists( 'wp_insert_attachment' ) ) {
			require_once( ABSPATH . 'wp-admin/includes/image.php' );
		}
		if ( ! function_exists( 'wp_generate_attachment_metadata' ) ) {
			require_once( ABSPATH . 'wp-admin/includes/media.php' );
		}

		$id = wp_insert_attachment( $attachment, $zip_path );

		if ( ! is_wp_error( $id ) ) {
			wp_update_attachment_metadata( $id, wp_generate_attachment_metadata( $id, $zip_path ) );
		}

		return $zip_url;
	}

	/**
	 * Generate random characters for backup file names
	 *
	 * @param int $min_length Minimum length
	 * @param int $max_length Maximum length
	 * @return string Random string
	 */
	private function get_random_characters( $min_length, $max_length ) {
		$characters = 'abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789';
		$rand_string = '';
		$length = wp_rand( $min_length, $max_length );

		for ( $count = 0; $count < $length; $count++ ) {
			$rand_string .= $characters[ wp_rand( 0, strlen( $characters ) - 1 ) ];
		}

		return $rand_string;
	}

	/**
	 * Safely set time limit without suppressing errors
	 *
	 * @param int $seconds Time limit in seconds
	 */
	private function set_time_limit_safely( $seconds ) {
		if ( function_exists( 'set_time_limit' ) && ! ini_get( 'safe_mode' ) ) {
			set_time_limit( $seconds );
		}
	}

	/**
	 * Set minimum memory limit safely
	 *
	 * @param string $new_memory_limit New memory limit
	 */
	private function set_minimum_memory_limit( $new_memory_limit ) {
		$memory_limit = ini_get( 'memory_limit' );

		if ( false === $memory_limit || empty( $memory_limit ) || -1 === (int) $memory_limit ) {
			return; // Unlimited memory or unable to get current limit
		}

		// Validate input
		if ( empty( $new_memory_limit ) || ! is_string( $new_memory_limit ) ) {
			return;
		}

		$unit = strtolower( substr( $memory_limit, -1 ) );
		$new_unit = strtolower( substr( $new_memory_limit, -1 ) );

		$memory_limit = (int) $memory_limit;
		$new_memory_limit = (int) $new_memory_limit;

		// Validate that we got valid numbers
		if ( $memory_limit <= 0 || $new_memory_limit <= 0 ) {
			return;
		}

		// Convert to bytes for comparison
		if ( 'm' === $unit ) {
			$memory_limit *= 1048576;
		} elseif ( 'g' === $unit ) {
			$memory_limit *= 1073741824;
		} elseif ( 'k' === $unit ) {
			$memory_limit *= 1024;
		}

		if ( 'm' === $new_unit ) {
			$new_memory_limit *= 1048576;
		} elseif ( 'g' === $new_unit ) {
			$new_memory_limit *= 1073741824;
		} elseif ( 'k' === $new_unit ) {
			$new_memory_limit *= 1024;
		}

		if ( $memory_limit < $new_memory_limit && function_exists( 'ini_set' ) ) {
			ini_set( 'memory_limit', $new_memory_limit . $new_unit );
		}
	}

	/**
	 * Optimize environment for backup creation
	 */
	private function optimize_for_backup_creation() {
		// Disable WordPress object cache during backup to save memory
		if ( function_exists( 'wp_cache_flush' ) ) {
			wp_cache_flush();
		}

		// Temporarily disable some WordPress hooks that might interfere
		$this->disable_unnecessary_hooks();

		// Optimize PHP settings for file operations
		if ( function_exists( 'ini_set' ) ) {
			// Increase max execution time for file operations
			ini_set( 'max_execution_time', 300 );

			// Optimize file upload settings
			ini_set( 'upload_max_filesize', '100M' );
			ini_set( 'post_max_size', '100M' );

			// Optimize memory for large file operations
			ini_set( 'memory_limit', '512M' );
		}
	}

	/**
	 * Disable unnecessary WordPress hooks during backup
	 */
	private function disable_unnecessary_hooks() {
		// Remove actions that might slow down file operations
		remove_action( 'wp_loaded', 'wp_schedule_update_checks' );
		remove_action( 'wp_version_check', 'wp_version_check' );
		remove_action( 'wp_update_plugins', 'wp_update_plugins' );
		remove_action( 'wp_update_themes', 'wp_update_themes' );
	}
}
