<?php

// Prevent direct access
if ( ! defined( 'ABSPATH' ) ) {
	exit;
}

final class CAJ_ETPU_Admin {

	/**
	 * Initialize the admin functionality
	 */
	public static function init() {
		add_action( 'load-update.php', array( __CLASS__, 'set_hooks' ) );

		// Initialize settings if available
		if ( file_exists( CAJ_ETPU_PLUGIN_DIR . 'includes/class-settings.php' ) ) {
			require_once( CAJ_ETPU_PLUGIN_DIR . 'includes/class-settings.php' );
			CAJ_ETPU_Settings::init();
		}
	}

	/**
	 * Set up hooks for theme and plugin uploads
	 */
	public static function set_hooks() {
		// Ensure we have the necessary WordPress upgrader classes
		if ( ! class_exists( 'WP_Upgrader' ) ) {
			require_once( ABSPATH . 'wp-admin/includes/class-wp-upgrader.php' );
		}

		add_action( 'admin_action_upload-theme', array( __CLASS__, 'update_theme' ) );
		add_action( 'admin_action_upload-plugin', array( __CLASS__, 'update_plugin' ) );
	}

	/**
	 * Handle theme upload and upgrade
	 */
	public static function update_theme() {
		// Security checks
		if ( ! current_user_can( 'upload_themes' ) ) {
			wp_die( esc_html__( 'Sorry, you are not allowed to install themes on this site.', 'easy-theme-and-plugin-upgrades' ) );
		}

		check_admin_referer( 'theme-upload' );

		// Additional security: Check if uploads are allowed
		if ( ! wp_is_file_mod_allowed( 'upload_themes' ) ) {
			wp_die( esc_html__( 'Sorry, theme uploads are disabled on this site.', 'easy-theme-and-plugin-upgrades' ) );
		}

		// Ensure File_Upload_Upgrader class is available
		if ( ! class_exists( 'File_Upload_Upgrader' ) ) {
			require_once( ABSPATH . 'wp-admin/includes/file.php' );
		}

		$file_upload = new File_Upload_Upgrader( 'themezip', 'package' );

		wp_enqueue_script( 'customize-loader' );

		$title = __( 'Upload Theme' );
		$parent_file = 'themes.php';
		$submenu_file = 'theme-install.php';

		require_once( ABSPATH . 'wp-admin/admin-header.php' );

		$title = sprintf( __( 'Installing Theme from uploaded file: %s' ), esc_html( basename( $file_upload->filename ) ) );
		$nonce = 'theme-upload';
		$url = add_query_arg( array( 'package' => $file_upload->id ), 'update.php?action=upload-theme' );
		$type = 'upload'; // Install plugin type, From Web or an Upload.

		require_once( plugin_dir_path( __FILE__ ) . 'custom-theme-upgrader.php' );

		$upgrader = new CAJ_ETPU_Theme_Upgrader( new Theme_Installer_Skin( compact( 'type', 'title', 'nonce', 'url' ) ) );
		$result = $upgrader->install( $file_upload->package );

		if ( $result || is_wp_error( $result ) ) {
			$file_upload->cleanup();
		}

		include( ABSPATH . 'wp-admin/admin-footer.php' );

		exit();
	}

	/**
	 * Handle plugin upload and upgrade
	 */
	public static function update_plugin() {
		// Security checks
		if ( ! current_user_can( 'upload_plugins' ) ) {
			wp_die( esc_html__( 'Sorry, you are not allowed to install plugins on this site.', 'easy-theme-and-plugin-upgrades' ) );
		}

		check_admin_referer( 'plugin-upload' );

		// Additional security: Check if uploads are allowed
		if ( ! wp_is_file_mod_allowed( 'upload_plugins' ) ) {
			wp_die( esc_html__( 'Sorry, plugin uploads are disabled on this site.', 'easy-theme-and-plugin-upgrades' ) );
		}

		// Ensure File_Upload_Upgrader class is available
		if ( ! class_exists( 'File_Upload_Upgrader' ) ) {
			require_once( ABSPATH . 'wp-admin/includes/file.php' );
		}

		$file_upload = new File_Upload_Upgrader( 'pluginzip', 'package' );

		$title = __( 'Upload Plugin' );
		$parent_file = 'plugins.php';
		$submenu_file = 'plugin-install.php';
		require_once( ABSPATH . 'wp-admin/admin-header.php' );

		$title = sprintf( __( 'Installing Plugin from uploaded file: %s' ), esc_html( basename( $file_upload->filename ) ) );
		$nonce = 'plugin-upload';
		$url = add_query_arg( array( 'package' => $file_upload->id ), 'update.php?action=upload-plugin' );
		$type = 'upload'; // Install plugin type, From Web or an Upload.

		require_once( plugin_dir_path( __FILE__ ) . 'custom-plugin-upgrader.php' );

		$upgrader = new CAJ_ETPU_Plugin_Upgrader( new Plugin_Installer_Skin( compact( 'type', 'title', 'nonce', 'url' ) ) );
		$result = $upgrader->install( $file_upload->package );

		if ( $result || is_wp_error( $result ) ) {
			$file_upload->cleanup();
		}

		include( ABSPATH . 'wp-admin/admin-footer.php' );

		exit();
	}
}
CAJ_ETPU_Admin::init();
