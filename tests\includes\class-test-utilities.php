<?php

/**
 * Test utilities for Easy Theme and Plugin Upgrades
 */
class CAJ_ETPU_Test_Utilities {
	
	/**
	 * Create a temporary theme directory for testing
	 *
	 * @param string $theme_name Theme name
	 * @param string $version Theme version
	 * @return string Path to created theme directory
	 */
	public static function create_test_theme( $theme_name = 'test-theme', $version = '1.0.0' ) {
		$temp_dir = sys_get_temp_dir() . '/caj-etpu-test-themes';
		$theme_dir = $temp_dir . '/' . $theme_name;
		
		// Create directory
		if ( ! is_dir( $theme_dir ) ) {
			wp_mkdir_p( $theme_dir );
		}
		
		// Create style.css
		$style_css = "/*
Theme Name: " . ucwords( str_replace( '-', ' ', $theme_name ) ) . "
Description: Test theme for Easy Theme and Plugin Upgrades
Version: {$version}
Author: Test Author
*/

body {
	font-family: Arial, sans-serif;
}";
		
		file_put_contents( $theme_dir . '/style.css', $style_css );
		
		// Create index.php
		$index_php = "<?php
// Test theme index file
get_header();
echo '<h1>Test Theme</h1>';
get_footer();
";
		
		file_put_contents( $theme_dir . '/index.php', $index_php );
		
		return $theme_dir;
	}
	
	/**
	 * Create a temporary plugin directory for testing
	 *
	 * @param string $plugin_name Plugin name
	 * @param string $version Plugin version
	 * @return string Path to created plugin directory
	 */
	public static function create_test_plugin( $plugin_name = 'test-plugin', $version = '1.0.0' ) {
		$temp_dir = sys_get_temp_dir() . '/caj-etpu-test-plugins';
		$plugin_dir = $temp_dir . '/' . $plugin_name;
		
		// Create directory
		if ( ! is_dir( $plugin_dir ) ) {
			wp_mkdir_p( $plugin_dir );
		}
		
		// Create main plugin file
		$plugin_php = "<?php
/*
Plugin Name: " . ucwords( str_replace( '-', ' ', $plugin_name ) ) . "
Description: Test plugin for Easy Theme and Plugin Upgrades
Version: {$version}
Author: Test Author
*/

// Prevent direct access
if ( ! defined( 'ABSPATH' ) ) {
	exit;
}

// Test plugin functionality
function test_plugin_init() {
	// Plugin initialization code
}
add_action( 'init', 'test_plugin_init' );
";
		
		file_put_contents( $plugin_dir . '/' . $plugin_name . '.php', $plugin_php );
		
		return $plugin_dir;
	}
	
	/**
	 * Create a test zip file from a directory
	 *
	 * @param string $source_dir Source directory
	 * @param string $zip_name Zip file name
	 * @return string Path to created zip file
	 */
	public static function create_test_zip( $source_dir, $zip_name = null ) {
		if ( ! $zip_name ) {
			$zip_name = basename( $source_dir ) . '.zip';
		}
		
		$zip_path = sys_get_temp_dir() . '/' . $zip_name;
		
		// Use WordPress PclZip for consistency
		require_once( ABSPATH . 'wp-admin/includes/class-pclzip.php' );
		
		$archive = new PclZip( $zip_path );
		$result = $archive->create( $source_dir, PCLZIP_OPT_REMOVE_PATH, dirname( $source_dir ) );
		
		if ( 0 === $result ) {
			throw new Exception( 'Failed to create test zip file: ' . $archive->errorInfo( true ) );
		}
		
		return $zip_path;
	}
	
	/**
	 * Clean up test files and directories
	 *
	 * @param array $paths Paths to clean up
	 */
	public static function cleanup_test_files( $paths = array() ) {
		$default_paths = array(
			sys_get_temp_dir() . '/caj-etpu-test-themes',
			sys_get_temp_dir() . '/caj-etpu-test-plugins',
		);
		
		$all_paths = array_merge( $default_paths, $paths );
		
		foreach ( $all_paths as $path ) {
			if ( file_exists( $path ) ) {
				if ( is_dir( $path ) ) {
					self::remove_directory( $path );
				} else {
					wp_delete_file( $path );
				}
			}
		}
	}
	
	/**
	 * Recursively remove a directory
	 *
	 * @param string $dir Directory path
	 */
	private static function remove_directory( $dir ) {
		if ( ! is_dir( $dir ) ) {
			return;
		}
		
		$files = array_diff( scandir( $dir ), array( '.', '..' ) );
		
		foreach ( $files as $file ) {
			$path = $dir . '/' . $file;
			if ( is_dir( $path ) ) {
				self::remove_directory( $path );
			} else {
				wp_delete_file( $path );
			}
		}
		
		rmdir( $dir );
	}
	
	/**
	 * Mock WordPress filesystem
	 */
	public static function mock_wp_filesystem() {
		global $wp_filesystem;
		
		if ( ! $wp_filesystem ) {
			require_once( ABSPATH . 'wp-admin/includes/file.php' );
			WP_Filesystem();
		}
	}
	
	/**
	 * Create a mock upload directory structure
	 *
	 * @return array Upload directory info
	 */
	public static function create_mock_upload_dir() {
		$upload_dir = sys_get_temp_dir() . '/caj-etpu-test-uploads';
		
		if ( ! is_dir( $upload_dir ) ) {
			wp_mkdir_p( $upload_dir );
		}
		
		return array(
			'path' => $upload_dir,
			'url' => 'http://example.com/wp-content/uploads',
			'subdir' => '',
			'basedir' => $upload_dir,
			'baseurl' => 'http://example.com/wp-content/uploads',
			'error' => false,
		);
	}
	
	/**
	 * Assert that a backup file was created
	 *
	 * @param string $backup_dir Backup directory
	 * @param string $item_name Item name
	 * @return string|false Backup file path or false if not found
	 */
	public static function assert_backup_created( $backup_dir, $item_name ) {
		$pattern = $backup_dir . '/' . $item_name . '-*.zip';
		$backup_files = glob( $pattern );
		
		if ( empty( $backup_files ) ) {
			return false;
		}
		
		// Return the most recent backup file
		usort( $backup_files, function( $a, $b ) {
			return filemtime( $b ) - filemtime( $a );
		} );
		
		return $backup_files[0];
	}
	
	/**
	 * Get test user with specific capabilities
	 *
	 * @param array $caps Capabilities to grant
	 * @return WP_User Test user
	 */
	public static function get_test_user( $caps = array( 'upload_themes', 'upload_plugins' ) ) {
		$user_id = wp_create_user( 'testuser', 'testpass', '<EMAIL>' );
		$user = new WP_User( $user_id );
		
		foreach ( $caps as $cap ) {
			$user->add_cap( $cap );
		}
		
		return $user;
	}
}
