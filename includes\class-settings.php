<?php

// Prevent direct access
if ( ! defined( 'ABSPATH' ) ) {
	exit;
}

/**
 * Settings class for Easy Theme and Plugin Upgrades
 * 
 * Handles plugin settings and configuration
 */
class CAJ_ETPU_Settings {
	
	/**
	 * Settings option name
	 */
	const OPTION_NAME = 'caj_etpu_settings';
	
	/**
	 * Settings page slug
	 */
	const PAGE_SLUG = 'caj-etpu-settings';
	
	/**
	 * Default settings
	 *
	 * @var array
	 */
	private static $default_settings = array(
		'enable_logging' => false,
		'auto_cleanup_backups' => false,
		'backup_retention_days' => 30,
		'show_progress_indicators' => true,
		'enhanced_error_messages' => true,
	);
	
	/**
	 * Initialize settings
	 */
	public static function init() {
		add_action( 'admin_menu', array( __CLASS__, 'add_admin_menu' ) );
		add_action( 'admin_init', array( __CLASS__, 'register_settings' ) );
		add_action( 'admin_enqueue_scripts', array( __CLASS__, 'enqueue_admin_scripts' ) );
	}
	
	/**
	 * Add admin menu
	 */
	public static function add_admin_menu() {
		add_options_page(
			__( 'Easy Theme and Plugin Upgrades Settings', 'easy-theme-and-plugin-upgrades' ),
			__( 'Theme & Plugin Upgrades', 'easy-theme-and-plugin-upgrades' ),
			'manage_options',
			self::PAGE_SLUG,
			array( __CLASS__, 'render_settings_page' )
		);
	}
	
	/**
	 * Register settings
	 */
	public static function register_settings() {
		register_setting(
			self::PAGE_SLUG,
			self::OPTION_NAME,
			array(
				'sanitize_callback' => array( __CLASS__, 'sanitize_settings' ),
				'default' => self::$default_settings,
			)
		);
		
		// General settings section
		add_settings_section(
			'general',
			__( 'General Settings', 'easy-theme-and-plugin-upgrades' ),
			array( __CLASS__, 'render_general_section' ),
			self::PAGE_SLUG
		);
		
		// Logging settings section
		add_settings_section(
			'logging',
			__( 'Logging & Debugging', 'easy-theme-and-plugin-upgrades' ),
			array( __CLASS__, 'render_logging_section' ),
			self::PAGE_SLUG
		);
		
		// Backup settings section
		add_settings_section(
			'backup',
			__( 'Backup Settings', 'easy-theme-and-plugin-upgrades' ),
			array( __CLASS__, 'render_backup_section' ),
			self::PAGE_SLUG
		);
		
		// Add settings fields
		self::add_settings_fields();
	}
	
	/**
	 * Add settings fields
	 */
	private static function add_settings_fields() {
		// Show progress indicators
		add_settings_field(
			'show_progress_indicators',
			__( 'Show Progress Indicators', 'easy-theme-and-plugin-upgrades' ),
			array( __CLASS__, 'render_checkbox_field' ),
			self::PAGE_SLUG,
			'general',
			array(
				'field' => 'show_progress_indicators',
				'description' => __( 'Display enhanced progress indicators during upgrades.', 'easy-theme-and-plugin-upgrades' ),
			)
		);
		
		// Enhanced error messages
		add_settings_field(
			'enhanced_error_messages',
			__( 'Enhanced Error Messages', 'easy-theme-and-plugin-upgrades' ),
			array( __CLASS__, 'render_checkbox_field' ),
			self::PAGE_SLUG,
			'general',
			array(
				'field' => 'enhanced_error_messages',
				'description' => __( 'Show detailed error messages with troubleshooting tips.', 'easy-theme-and-plugin-upgrades' ),
			)
		);
		
		// Enable logging
		add_settings_field(
			'enable_logging',
			__( 'Enable Logging', 'easy-theme-and-plugin-upgrades' ),
			array( __CLASS__, 'render_checkbox_field' ),
			self::PAGE_SLUG,
			'logging',
			array(
				'field' => 'enable_logging',
				'description' => __( 'Enable detailed logging for troubleshooting upgrade issues.', 'easy-theme-and-plugin-upgrades' ),
			)
		);
		
		// Auto cleanup backups
		add_settings_field(
			'auto_cleanup_backups',
			__( 'Auto-cleanup Old Backups', 'easy-theme-and-plugin-upgrades' ),
			array( __CLASS__, 'render_checkbox_field' ),
			self::PAGE_SLUG,
			'backup',
			array(
				'field' => 'auto_cleanup_backups',
				'description' => __( 'Automatically remove backup files older than the specified retention period.', 'easy-theme-and-plugin-upgrades' ),
			)
		);
		
		// Backup retention days
		add_settings_field(
			'backup_retention_days',
			__( 'Backup Retention (Days)', 'easy-theme-and-plugin-upgrades' ),
			array( __CLASS__, 'render_number_field' ),
			self::PAGE_SLUG,
			'backup',
			array(
				'field' => 'backup_retention_days',
				'description' => __( 'Number of days to keep backup files before auto-cleanup.', 'easy-theme-and-plugin-upgrades' ),
				'min' => 1,
				'max' => 365,
			)
		);
	}
	
	/**
	 * Render settings page
	 */
	public static function render_settings_page() {
		if ( ! current_user_can( 'manage_options' ) ) {
			wp_die( esc_html__( 'You do not have sufficient permissions to access this page.', 'easy-theme-and-plugin-upgrades' ) );
		}
		
		?>
		<div class="wrap">
			<h1><?php echo esc_html( get_admin_page_title() ); ?></h1>
			
			<form method="post" action="options.php">
				<?php
				settings_fields( self::PAGE_SLUG );
				do_settings_sections( self::PAGE_SLUG );
				submit_button();
				?>
			</form>
			
			<div class="caj-etpu-settings-info" style="margin-top: 30px; padding: 20px; background: #f9f9f9; border-radius: 4px;">
				<h3><?php esc_html_e( 'Plugin Information', 'easy-theme-and-plugin-upgrades' ); ?></h3>
				<p><strong><?php esc_html_e( 'Version:', 'easy-theme-and-plugin-upgrades' ); ?></strong> <?php echo esc_html( defined( 'CAJ_ETPU_VERSION' ) ? CAJ_ETPU_VERSION : '3.0.0' ); ?></p>
				<p><strong><?php esc_html_e( 'WordPress Version:', 'easy-theme-and-plugin-upgrades' ); ?></strong> <?php echo esc_html( get_bloginfo( 'version' ) ); ?></p>
				<p><strong><?php esc_html_e( 'PHP Version:', 'easy-theme-and-plugin-upgrades' ); ?></strong> <?php echo esc_html( PHP_VERSION ); ?></p>
				
				<?php if ( class_exists( 'CAJ_ETPU_Logger' ) && CAJ_ETPU_Logger::is_logging_enabled() ) : ?>
					<p><strong><?php esc_html_e( 'Log File:', 'easy-theme-and-plugin-upgrades' ); ?></strong> 
						<code><?php echo esc_html( CAJ_ETPU_Logger::get_log_file() ); ?></code>
					</p>
				<?php endif; ?>
			</div>
		</div>
		<?php
	}
	
	/**
	 * Render general section
	 */
	public static function render_general_section() {
		echo '<p>' . esc_html__( 'Configure general plugin behavior and user interface options.', 'easy-theme-and-plugin-upgrades' ) . '</p>';
	}
	
	/**
	 * Render logging section
	 */
	public static function render_logging_section() {
		echo '<p>' . esc_html__( 'Configure logging and debugging options to help troubleshoot upgrade issues.', 'easy-theme-and-plugin-upgrades' ) . '</p>';
	}
	
	/**
	 * Render backup section
	 */
	public static function render_backup_section() {
		echo '<p>' . esc_html__( 'Configure backup creation and management options.', 'easy-theme-and-plugin-upgrades' ) . '</p>';
	}
	
	/**
	 * Render checkbox field
	 */
	public static function render_checkbox_field( $args ) {
		$settings = self::get_settings();
		$field = $args['field'];
		$checked = isset( $settings[ $field ] ) ? $settings[ $field ] : false;
		
		printf(
			'<input type="checkbox" id="%1$s" name="%2$s[%1$s]" value="1" %3$s />
			<label for="%1$s">%4$s</label>',
			esc_attr( $field ),
			esc_attr( self::OPTION_NAME ),
			checked( $checked, true, false ),
			esc_html( $args['description'] )
		);
	}
	
	/**
	 * Render number field
	 */
	public static function render_number_field( $args ) {
		$settings = self::get_settings();
		$field = $args['field'];
		$value = isset( $settings[ $field ] ) ? $settings[ $field ] : '';
		
		printf(
			'<input type="number" id="%1$s" name="%2$s[%1$s]" value="%3$s" min="%4$d" max="%5$d" class="small-text" />
			<p class="description">%6$s</p>',
			esc_attr( $field ),
			esc_attr( self::OPTION_NAME ),
			esc_attr( $value ),
			(int) $args['min'],
			(int) $args['max'],
			esc_html( $args['description'] )
		);
	}
	
	/**
	 * Sanitize settings
	 */
	public static function sanitize_settings( $input ) {
		$sanitized = array();
		
		// Sanitize checkbox fields
		$checkbox_fields = array( 'enable_logging', 'auto_cleanup_backups', 'show_progress_indicators', 'enhanced_error_messages' );
		foreach ( $checkbox_fields as $field ) {
			$sanitized[ $field ] = isset( $input[ $field ] ) && $input[ $field ];
		}
		
		// Sanitize number fields
		$sanitized['backup_retention_days'] = isset( $input['backup_retention_days'] ) 
			? max( 1, min( 365, (int) $input['backup_retention_days'] ) )
			: 30;
		
		return $sanitized;
	}
	
	/**
	 * Get settings
	 */
	public static function get_settings() {
		return wp_parse_args( get_option( self::OPTION_NAME, array() ), self::$default_settings );
	}
	
	/**
	 * Get a specific setting
	 */
	public static function get_setting( $key, $default = null ) {
		$settings = self::get_settings();
		return isset( $settings[ $key ] ) ? $settings[ $key ] : $default;
	}
	
	/**
	 * Enqueue admin scripts
	 */
	public static function enqueue_admin_scripts( $hook ) {
		if ( 'settings_page_' . self::PAGE_SLUG !== $hook ) {
			return;
		}
		
		if ( defined( 'CAJ_ETPU_PLUGIN_URL' ) && defined( 'CAJ_ETPU_VERSION' ) ) {
			wp_enqueue_style( 'caj-etpu-admin', CAJ_ETPU_PLUGIN_URL . 'assets/admin.css', array(), CAJ_ETPU_VERSION );
		}
	}
}
