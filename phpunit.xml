<?xml version="1.0"?>
<phpunit
	bootstrap="tests/bootstrap.php"
	backupGlobals="false"
	colors="true"
	convertErrorsToExceptions="true"
	convertNoticesToExceptions="true"
	convertWarningsToExceptions="true"
	>
	<testsuites>
		<testsuite name="Easy Theme and Plugin Upgrades Test Suite">
			<directory>./tests/</directory>
		</testsuite>
	</testsuites>
	
	<filter>
		<whitelist processUncoveredFilesFromWhitelist="true">
			<directory suffix=".php">./</directory>
			<exclude>
				<directory>./tests/</directory>
				<directory>./trunk/</directory>
				<file>./uninstall.php</file>
			</exclude>
		</whitelist>
	</filter>
	
	<logging>
		<log type="coverage-html" target="./tests/coverage"/>
		<log type="coverage-text" target="php://stdout" showUncoveredFiles="false"/>
	</logging>
</phpunit>
