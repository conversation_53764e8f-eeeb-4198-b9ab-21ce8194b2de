/**
 * Admin styles for Easy Theme and Plugin Upgrades
 */

/* Settings page styles */
.caj-etpu-settings-info {
	background: #f9f9f9;
	border: 1px solid #ddd;
	border-radius: 4px;
	padding: 20px;
	margin-top: 30px;
}

.caj-etpu-settings-info h3 {
	margin-top: 0;
	color: #333;
}

.caj-etpu-settings-info p {
	margin: 10px 0;
}

.caj-etpu-settings-info code {
	background: #fff;
	padding: 2px 6px;
	border-radius: 3px;
	font-size: 12px;
}

/* Progress indicators */
.caj-etpu-progress-indicator {
	margin: 15px 0;
	padding: 15px;
	background: #fff;
	border: 1px solid #ddd;
	border-radius: 4px;
	box-shadow: 0 1px 3px rgba(0,0,0,0.1);
}

.caj-etpu-progress-indicator p {
	margin: 0 0 10px 0;
	font-weight: 600;
	color: #333;
}

.caj-etpu-progress-bar {
	background: #f1f1f1;
	border-radius: 3px;
	margin: 10px 0;
	overflow: hidden;
}

.caj-etpu-progress-fill {
	background: linear-gradient(90deg, #0073aa, #005a87);
	height: 20px;
	border-radius: 3px;
	transition: width 0.3s ease;
	position: relative;
}

.caj-etpu-progress-fill::after {
	content: '';
	position: absolute;
	top: 0;
	left: 0;
	right: 0;
	bottom: 0;
	background: linear-gradient(
		90deg,
		transparent,
		rgba(255,255,255,0.2),
		transparent
	);
	animation: shimmer 2s infinite;
}

@keyframes shimmer {
	0% { transform: translateX(-100%); }
	100% { transform: translateX(100%); }
}

/* Enhanced notices */
.notice.caj-etpu-notice {
	border-left-width: 4px;
	padding: 12px;
}

.notice.caj-etpu-notice .dashicons {
	margin-right: 8px;
	vertical-align: middle;
}

.notice.caj-etpu-notice p {
	margin: 0;
	display: flex;
	align-items: center;
}

/* Backup info styling */
.caj-etpu-backup-info {
	background: #d4edda;
	border: 1px solid #c3e6cb;
	border-radius: 4px;
	padding: 15px;
	margin: 15px 0;
}

.caj-etpu-backup-info .dashicons-yes-alt {
	color: #155724;
	margin-right: 8px;
}

.caj-etpu-backup-info a {
	color: #155724;
	text-decoration: none;
	font-weight: 600;
}

.caj-etpu-backup-info a:hover {
	text-decoration: underline;
}

/* Error messages with tips */
.caj-etpu-error-with-tips {
	background: #f8d7da;
	border: 1px solid #f5c6cb;
	border-radius: 4px;
	padding: 15px;
	margin: 15px 0;
}

.caj-etpu-error-with-tips .dashicons-dismiss {
	color: #721c24;
	margin-right: 8px;
}

.caj-etpu-error-with-tips ul {
	margin: 10px 0 0 20px;
	color: #721c24;
}

.caj-etpu-error-with-tips li {
	margin: 5px 0;
}

/* Settings form improvements */
.form-table th {
	width: 200px;
}

.form-table td input[type="checkbox"] {
	margin-right: 8px;
}

.form-table .description {
	font-style: italic;
	color: #666;
}

/* Responsive adjustments */
@media screen and (max-width: 782px) {
	.caj-etpu-progress-indicator {
		padding: 10px;
	}
	
	.caj-etpu-settings-info {
		padding: 15px;
	}
	
	.form-table th {
		width: auto;
	}
}

/* Animation for success messages */
.notice-success.is-dismissible {
	animation: slideInFromTop 0.3s ease-out;
}

@keyframes slideInFromTop {
	0% {
		opacity: 0;
		transform: translateY(-20px);
	}
	100% {
		opacity: 1;
		transform: translateY(0);
	}
}

/* Loading spinner for upgrade process */
.caj-etpu-loading {
	display: inline-block;
	width: 16px;
	height: 16px;
	border: 2px solid #f3f3f3;
	border-top: 2px solid #0073aa;
	border-radius: 50%;
	animation: spin 1s linear infinite;
	margin-right: 8px;
	vertical-align: middle;
}

@keyframes spin {
	0% { transform: rotate(0deg); }
	100% { transform: rotate(360deg); }
}
