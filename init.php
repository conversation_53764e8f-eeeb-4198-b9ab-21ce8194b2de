<?php

/*
Plugin Name: Easy Theme and Plugin Upgrades
Plugin URI:  https://wordpress.org/plugins/easy-theme-and-plugin-upgrades/
Description: Upgrade themes and plugins using a zip file without having to remove them first.
Author:      <PERSON>
Author URI:  https://chrisjean.com/
Version:     3.0.0
Requires at least: 6.0
Tested up to: 6.8
Requires PHP: 7.4
License:     GPLv2 or later
License URI: https://www.gnu.org/licenses/gpl-2.0.html
Text Domain: easy-theme-and-plugin-upgrades
Network:     false

Easy Theme and Plugin Upgrades is free software you can redistribute
it and/or modify it under the terms of the GNU General Public License
as published by the Free Software Foundation, either version 2 of the
License, or any later version.

Easy Theme and Plugin Upgrades is distributed in the hope that it
will be useful, but WITHOUT ANY WARRANTY; without even the implied
warranty of MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE. See
the GNU General Public License for more details.

You should have received a copy of the GNU General Public License
along with Easy Theme and Plugin Upgrades. If not, see
https://www.gnu.org/licenses/gpl-2.0.html.
*/

// Prevent direct access
if ( ! defined( 'ABSPATH' ) ) {
	exit;
}

// Define plugin constants
define( 'CAJ_ETPU_VERSION', '3.0.0' );
define( 'CAJ_ETPU_PLUGIN_FILE', __FILE__ );
define( 'CAJ_ETPU_PLUGIN_DIR', plugin_dir_path( __FILE__ ) );
define( 'CAJ_ETPU_PLUGIN_URL', plugin_dir_url( __FILE__ ) );

/**
 * Plugin activation hook
 */
function caj_etpu_activate() {
	// Check WordPress version
	if ( version_compare( get_bloginfo( 'version' ), '6.0', '<' ) ) {
		deactivate_plugins( plugin_basename( __FILE__ ) );
		wp_die(
			esc_html__( 'Easy Theme and Plugin Upgrades requires WordPress 6.0 or higher.', 'easy-theme-and-plugin-upgrades' ),
			esc_html__( 'Plugin Activation Error', 'easy-theme-and-plugin-upgrades' ),
			array( 'back_link' => true )
		);
	}

	// Check PHP version
	if ( version_compare( PHP_VERSION, '7.4', '<' ) ) {
		deactivate_plugins( plugin_basename( __FILE__ ) );
		wp_die(
			esc_html__( 'Easy Theme and Plugin Upgrades requires PHP 7.4 or higher.', 'easy-theme-and-plugin-upgrades' ),
			esc_html__( 'Plugin Activation Error', 'easy-theme-and-plugin-upgrades' ),
			array( 'back_link' => true )
		);
	}

	// Set plugin version option
	update_option( 'caj_etpu_version', CAJ_ETPU_VERSION );

	// Set activation timestamp
	update_option( 'caj_etpu_activated', time() );
}

/**
 * Plugin deactivation hook
 */
function caj_etpu_deactivate() {
	// Clean up any temporary files or scheduled events if needed
	// Note: We don't delete user data on deactivation, only on uninstall
	delete_option( 'caj_etpu_activated' );
}

/**
 * Plugin uninstall hook (defined in separate uninstall.php file)
 */

// Register activation and deactivation hooks
register_activation_hook( __FILE__, 'caj_etpu_activate' );
register_deactivation_hook( __FILE__, 'caj_etpu_deactivate' );

// Initialize the plugin
if ( is_admin() ) {
	require_once( CAJ_ETPU_PLUGIN_DIR . 'admin.php' );

	// Initialize admin functionality on proper hook
	add_action( 'admin_init', array( 'CAJ_ETPU_Admin', 'init' ) );
}
