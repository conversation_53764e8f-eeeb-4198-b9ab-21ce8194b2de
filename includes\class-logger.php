<?php

// Prevent direct access
if ( ! defined( 'ABSPATH' ) ) {
	exit;
}

/**
 * Logger class for Easy Theme and Plugin Upgrades
 * 
 * Provides logging functionality for debugging and troubleshooting
 */
class CAJ_ETPU_Logger {
	
	/**
	 * Log levels
	 */
	const LEVEL_ERROR = 'error';
	const LEVEL_WARNING = 'warning';
	const LEVEL_INFO = 'info';
	const LEVEL_DEBUG = 'debug';
	
	/**
	 * Maximum log file size in bytes (1MB)
	 */
	const MAX_LOG_SIZE = 1048576;
	
	/**
	 * Log file path
	 *
	 * @var string
	 */
	private static $log_file = null;
	
	/**
	 * Whether logging is enabled
	 *
	 * @var bool
	 */
	private static $logging_enabled = null;
	
	/**
	 * Initialize the logger
	 */
	public static function init() {
		if ( is_null( self::$log_file ) ) {
			$upload_dir = wp_upload_dir();
			self::$log_file = $upload_dir['basedir'] . '/caj-etpu-debug.log';
		}
		
		if ( is_null( self::$logging_enabled ) ) {
			// Enable logging if WP_DEBUG is on or if explicitly enabled in settings
			self::$logging_enabled = defined( 'WP_DEBUG' ) && WP_DEBUG;
		}
	}
	
	/**
	 * Log a message
	 *
	 * @param string $message Log message
	 * @param string $level Log level
	 * @param array $context Additional context data
	 */
	public static function log( $message, $level = self::LEVEL_INFO, $context = array() ) {
		self::init();
		
		if ( ! self::$logging_enabled ) {
			return;
		}
		
		// Prepare log entry
		$timestamp = current_time( 'Y-m-d H:i:s' );
		$level = strtoupper( $level );
		
		// Add context if provided
		$context_str = '';
		if ( ! empty( $context ) ) {
			$context_str = ' | Context: ' . wp_json_encode( $context );
		}
		
		$log_entry = sprintf(
			"[%s] [%s] %s%s\n",
			$timestamp,
			$level,
			$message,
			$context_str
		);
		
		// Check log file size and rotate if necessary
		self::rotate_log_if_needed();
		
		// Write to log file
		error_log( $log_entry, 3, self::$log_file );
	}
	
	/**
	 * Log an error message
	 *
	 * @param string $message Error message
	 * @param array $context Additional context data
	 */
	public static function error( $message, $context = array() ) {
		self::log( $message, self::LEVEL_ERROR, $context );
	}
	
	/**
	 * Log a warning message
	 *
	 * @param string $message Warning message
	 * @param array $context Additional context data
	 */
	public static function warning( $message, $context = array() ) {
		self::log( $message, self::LEVEL_WARNING, $context );
	}
	
	/**
	 * Log an info message
	 *
	 * @param string $message Info message
	 * @param array $context Additional context data
	 */
	public static function info( $message, $context = array() ) {
		self::log( $message, self::LEVEL_INFO, $context );
	}
	
	/**
	 * Log a debug message
	 *
	 * @param string $message Debug message
	 * @param array $context Additional context data
	 */
	public static function debug( $message, $context = array() ) {
		self::log( $message, self::LEVEL_DEBUG, $context );
	}
	
	/**
	 * Enable or disable logging
	 *
	 * @param bool $enabled Whether to enable logging
	 */
	public static function set_logging_enabled( $enabled ) {
		self::$logging_enabled = (bool) $enabled;
	}
	
	/**
	 * Check if logging is enabled
	 *
	 * @return bool
	 */
	public static function is_logging_enabled() {
		self::init();
		return self::$logging_enabled;
	}
	
	/**
	 * Get the log file path
	 *
	 * @return string
	 */
	public static function get_log_file() {
		self::init();
		return self::$log_file;
	}
	
	/**
	 * Clear the log file
	 */
	public static function clear_log() {
		self::init();
		if ( file_exists( self::$log_file ) ) {
			wp_delete_file( self::$log_file );
		}
	}
	
	/**
	 * Rotate log file if it's too large
	 */
	private static function rotate_log_if_needed() {
		if ( ! file_exists( self::$log_file ) ) {
			return;
		}
		
		if ( filesize( self::$log_file ) > self::MAX_LOG_SIZE ) {
			// Rename current log to .old
			$old_log = self::$log_file . '.old';
			if ( file_exists( $old_log ) ) {
				wp_delete_file( $old_log );
			}
			rename( self::$log_file, $old_log );
		}
	}
	
	/**
	 * Get recent log entries
	 *
	 * @param int $lines Number of lines to retrieve
	 * @return array
	 */
	public static function get_recent_entries( $lines = 50 ) {
		self::init();
		
		if ( ! file_exists( self::$log_file ) ) {
			return array();
		}
		
		$file_lines = file( self::$log_file, FILE_IGNORE_NEW_LINES | FILE_SKIP_EMPTY_LINES );
		if ( ! $file_lines ) {
			return array();
		}
		
		return array_slice( $file_lines, -$lines );
	}
}
