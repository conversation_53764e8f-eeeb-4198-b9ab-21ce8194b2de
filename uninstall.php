<?php

// Prevent direct access
if ( ! defined( 'WP_UNINSTALL_PLUGIN' ) ) {
	exit;
}

/**
 * Easy Theme and Plugin Upgrades Uninstall
 * 
 * This file is executed when the plugin is deleted through the WordPress admin.
 * It cleans up any data created by the plugin.
 */

// Remove plugin options
delete_option( 'caj_etpu_version' );
delete_option( 'caj_etpu_activated' );
delete_option( 'caj_etpu_settings' );

// Remove any transients
delete_transient( 'caj_etpu_backup_status' );

// Clean up any backup files older than 30 days (optional)
// This is commented out by default to preserve user backups
/*
$upload_dir = wp_upload_dir();
$backup_pattern = $upload_dir['basedir'] . '/*-backup-*.zip';
$backup_files = glob( $backup_pattern );

if ( $backup_files ) {
	$thirty_days_ago = time() - ( 30 * DAY_IN_SECONDS );
	
	foreach ( $backup_files as $backup_file ) {
		if ( filemtime( $backup_file ) < $thirty_days_ago ) {
			// Check if this is actually a backup file created by our plugin
			if ( strpos( basename( $backup_file ), '-backup-' ) !== false ) {
				wp_delete_file( $backup_file );
				
				// Also remove from media library if it exists
				$upload_dir = wp_upload_dir();
				$attachment_id = attachment_url_to_postid( $upload_dir['url'] . '/' . basename( $backup_file ) );
				if ( $attachment_id ) {
					wp_delete_attachment( $attachment_id, true );
				}
			}
		}
	}
}
*/

// Remove any custom database tables (none in this plugin)
// Remove any custom user meta (none in this plugin)
// Remove any custom post types or taxonomies (none in this plugin)

// Clear any cached data
wp_cache_flush();
