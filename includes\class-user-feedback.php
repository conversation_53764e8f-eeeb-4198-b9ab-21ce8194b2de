<?php

// Prevent direct access
if ( ! defined( 'ABSPATH' ) ) {
	exit;
}

/**
 * User Feedback class for Easy Theme and Plugin Upgrades
 * 
 * Provides enhanced user feedback and progress indicators
 */
class CAJ_ETPU_User_Feedback {
	
	/**
	 * Message types
	 */
	const TYPE_SUCCESS = 'success';
	const TYPE_ERROR = 'error';
	const TYPE_WARNING = 'warning';
	const TYPE_INFO = 'info';
	
	/**
	 * Display a formatted feedback message
	 *
	 * @param string $message The message to display
	 * @param string $type Message type (success, error, warning, info)
	 * @param bool $dismissible Whether the message is dismissible
	 */
	public static function display_message( $message, $type = self::TYPE_INFO, $dismissible = true ) {
		$dismissible_class = $dismissible ? 'is-dismissible' : '';
		$icon = self::get_icon_for_type( $type );
		
		printf(
			'<div class="notice notice-%s %s"><p>%s <strong>%s:</strong> %s</p></div>',
			esc_attr( $type ),
			esc_attr( $dismissible_class ),
			$icon,
			esc_html( self::get_label_for_type( $type ) ),
			wp_kses_post( $message )
		);
	}
	
	/**
	 * Display a progress indicator
	 *
	 * @param string $message Progress message
	 * @param int $current Current step
	 * @param int $total Total steps
	 */
	public static function display_progress( $message, $current = 0, $total = 0 ) {
		$progress_html = '';
		
		if ( $total > 0 ) {
			$percentage = min( 100, ( $current / $total ) * 100 );
			$progress_html = sprintf(
				'<div class="caj-etpu-progress-bar" style="background: #f1f1f1; border-radius: 3px; margin: 10px 0;">
					<div class="caj-etpu-progress-fill" style="background: #0073aa; height: 20px; border-radius: 3px; width: %d%%; transition: width 0.3s ease;"></div>
				</div>
				<p style="margin: 5px 0; font-size: 12px; color: #666;">%s (%d/%d)</p>',
				(int) $percentage,
				esc_html( sprintf( __( 'Step %d of %d', 'easy-theme-and-plugin-upgrades' ), $current, $total ) ),
				$current,
				$total
			);
		}
		
		printf(
			'<div class="caj-etpu-progress-indicator" style="margin: 15px 0; padding: 15px; background: #fff; border: 1px solid #ddd; border-radius: 4px;">
				<p style="margin: 0 0 10px 0; font-weight: 600;">%s</p>
				%s
			</div>',
			wp_kses_post( $message ),
			$progress_html
		);
	}
	
	/**
	 * Display backup information
	 *
	 * @param string $backup_url URL to the backup file
	 * @param string $item_name Name of the backed up item
	 * @param string $version Version of the backed up item
	 */
	public static function display_backup_info( $backup_url, $item_name, $version ) {
		$message = sprintf(
			/* translators: 1: item name, 2: version, 3: backup download link */
			__( 'A backup of %1$s (version %2$s) has been created. You can <a href="%3$s" target="_blank">download the backup file</a> for safekeeping.', 'easy-theme-and-plugin-upgrades' ),
			'<strong>' . esc_html( $item_name ) . '</strong>',
			'<strong>' . esc_html( $version ) . '</strong>',
			esc_url( $backup_url )
		);
		
		self::display_message( $message, self::TYPE_SUCCESS );
	}
	
	/**
	 * Display upgrade completion message
	 *
	 * @param string $item_name Name of the upgraded item
	 * @param string $old_version Previous version
	 * @param string $new_version New version
	 */
	public static function display_upgrade_complete( $item_name, $old_version, $new_version ) {
		$message = sprintf(
			/* translators: 1: item name, 2: old version, 3: new version */
			__( '%1$s has been successfully upgraded from version %2$s to %3$s.', 'easy-theme-and-plugin-upgrades' ),
			'<strong>' . esc_html( $item_name ) . '</strong>',
			'<strong>' . esc_html( $old_version ) . '</strong>',
			'<strong>' . esc_html( $new_version ) . '</strong>'
		);
		
		self::display_message( $message, self::TYPE_SUCCESS );
	}
	
	/**
	 * Display error with troubleshooting tips
	 *
	 * @param string $error_message The error message
	 * @param array $troubleshooting_tips Array of troubleshooting tips
	 */
	public static function display_error_with_tips( $error_message, $troubleshooting_tips = array() ) {
		$tips_html = '';
		
		if ( ! empty( $troubleshooting_tips ) ) {
			$tips_html = '<div style="margin-top: 15px;"><strong>' . 
				esc_html__( 'Troubleshooting tips:', 'easy-theme-and-plugin-upgrades' ) . 
				'</strong><ul style="margin: 5px 0 0 20px;">';
			
			foreach ( $troubleshooting_tips as $tip ) {
				$tips_html .= '<li style="margin: 3px 0;">' . wp_kses_post( $tip ) . '</li>';
			}
			
			$tips_html .= '</ul></div>';
		}
		
		$full_message = wp_kses_post( $error_message ) . $tips_html;
		self::display_message( $full_message, self::TYPE_ERROR, false );
	}
	
	/**
	 * Get icon for message type
	 *
	 * @param string $type Message type
	 * @return string Icon HTML
	 */
	private static function get_icon_for_type( $type ) {
		$icons = array(
			self::TYPE_SUCCESS => '<span class="dashicons dashicons-yes-alt" style="color: #46b450;"></span>',
			self::TYPE_ERROR   => '<span class="dashicons dashicons-dismiss" style="color: #dc3232;"></span>',
			self::TYPE_WARNING => '<span class="dashicons dashicons-warning" style="color: #ffb900;"></span>',
			self::TYPE_INFO    => '<span class="dashicons dashicons-info" style="color: #0073aa;"></span>',
		);
		
		return isset( $icons[ $type ] ) ? $icons[ $type ] : $icons[ self::TYPE_INFO ];
	}
	
	/**
	 * Get label for message type
	 *
	 * @param string $type Message type
	 * @return string Label
	 */
	private static function get_label_for_type( $type ) {
		$labels = array(
			self::TYPE_SUCCESS => __( 'Success', 'easy-theme-and-plugin-upgrades' ),
			self::TYPE_ERROR   => __( 'Error', 'easy-theme-and-plugin-upgrades' ),
			self::TYPE_WARNING => __( 'Warning', 'easy-theme-and-plugin-upgrades' ),
			self::TYPE_INFO    => __( 'Info', 'easy-theme-and-plugin-upgrades' ),
		);
		
		return isset( $labels[ $type ] ) ? $labels[ $type ] : $labels[ self::TYPE_INFO ];
	}
	
	/**
	 * Add JavaScript for enhanced UI interactions
	 */
	public static function add_ui_scripts() {
		?>
		<script type="text/javascript">
		jQuery(document).ready(function($) {
			// Auto-hide success messages after 5 seconds
			setTimeout(function() {
				$('.notice-success.is-dismissible').fadeOut();
			}, 5000);
			
			// Add smooth animations to progress bars
			$('.caj-etpu-progress-fill').each(function() {
				var width = $(this).css('width');
				$(this).css('width', '0%').animate({width: width}, 1000);
			});
		});
		</script>
		<?php
	}
}
